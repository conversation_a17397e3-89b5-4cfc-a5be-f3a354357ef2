# Level 1: Beginner Humanoid Robot Workshop
## 3-Day Build-Your-Own Moving Robot

### 🎯 Workshop Overview
**Target Audience:** Ages 12-15, No prior programming experience required  
**Duration:** 3 days (6 hours per day)  
**Objective:** Build a simple moving humanoid robot that can be controlled via smartphone

### 🤖 What You'll Build
By the end of this workshop, you'll have created:
- A two-wheeled moving robot with basic humanoid features
- Head that can turn left and right
- One arm that can wave
- Smartphone web control interface
- LED status indicators

### 📋 Prerequisites
- Basic computer skills
- Enthusiasm to learn!
- No programming experience required

### ⚠️ Safety Guidelines
- Always disconnect power when wiring
- Handle components gently
- Ask for help if unsure
- Wear safety glasses when soldering (if required)

## 🛠️ Components List

### Core Electronics
- 1x ESP32 DevKit V1 microcontroller
- 1x Breadboard (830 points)
- 1x USB-C cable for ESP32
- 1x 7.4V Li-Po battery pack (2000mAh)
- 1x Battery connector with switch

### Motors & Movement
- 2x DC gear motors (6V, with wheels)
- 1x L298N motor driver module
- 2x MG90S servo motors (for head movement)
- 1x SG90 servo motor (for arm movement)

### Structure & Mounting
- 1x Robot chassis kit (acrylic or 3D printed)
- 4x Motor mounting brackets
- 1x Servo mounting brackets
- Assorted screws and standoffs
- 1x Simple humanoid head (3D printed)
- 1x Simple arm attachment

### Indicators & Feedback
- 3x LEDs (Red, Green, Blue)
- 3x 220Ω resistors
- 1x Buzzer (optional)

### Wiring & Connections
- Jumper wires (male-to-male, male-to-female)
- Wire strippers
- Small screwdriver set

## 📅 3-Day Schedule

### Day 1: Assembly & First Steps (6 hours)
**Morning (3 hours):**
- Introduction to robotics and components (30 min)
- Safety briefing and tool introduction (30 min)
- Chassis assembly and motor mounting (2 hours)

**Afternoon (3 hours):**
- ESP32 introduction and setup (1 hour)
- Basic wiring and connections (1.5 hours)
- First program: LED blink test (30 min)

**Day 1 Milestone:** Robot chassis assembled, ESP32 programmed with basic LED control

### Day 2: Movement & Control (6 hours)
**Morning (3 hours):**
- Motor control programming (1.5 hours)
- Basic movement commands (forward, backward, stop) (1.5 hours)

**Afternoon (3 hours):**
- Servo motor programming (1 hour)
- Head movement implementation (1 hour)
- Arm waving gesture (1 hour)

**Day 2 Milestone:** Robot can move and perform basic head/arm movements

### Day 3: Smartphone Control & Final Testing (6 hours)
**Morning (3 hours):**
- WiFi setup and web server programming (2 hours)
- Creating smartphone control interface (1 hour)

**Afternoon (3 hours):**
- Integration testing and debugging (1.5 hours)
- Final demonstrations and improvements (1 hour)
- Celebration and next steps (30 min)

**Day 3 Milestone:** Complete robot controllable via smartphone

## 🔧 Step-by-Step Instructions

### Day 1: Hardware Assembly

#### Step 1: Chassis Assembly (1 hour)
1. Unpack chassis components
2. Attach motor mounting brackets to chassis base
3. Mount DC motors to brackets
4. Attach wheels to motors
5. Install battery compartment

#### Step 2: ESP32 Setup (1 hour)
1. Install Arduino IDE on computer
2. Add ESP32 board support
3. Connect ESP32 to computer via USB
4. Test connection with simple blink program

```cpp
// Day 1 Test Program - LED Blink
void setup() {
  pinMode(2, OUTPUT); // Built-in LED
  Serial.begin(115200);
  Serial.println("Robot starting up!");
}

void loop() {
  digitalWrite(2, HIGH);
  delay(1000);
  digitalWrite(2, LOW);
  delay(1000);
}
```

#### Step 3: Basic Wiring (2 hours)
1. Connect motor driver to ESP32
2. Wire DC motors to motor driver
3. Connect servo motors for head and arm
4. Add status LEDs
5. Connect battery power (with switch)

**Wiring Diagram:**
```
ESP32 Pin Connections:
- Pin 25, 26: Left motor (via L298N)
- Pin 14, 15: Right motor (via L298N)
- Pin 18: Head pan servo
- Pin 19: Head tilt servo
- Pin 5: Arm servo
- Pin 2: Status LED (built-in)
- Pin 4: External LED
```

### Day 2: Programming Movement

#### Step 4: Motor Control Programming (1.5 hours)

```cpp
// Day 2 Motor Control Program
#include <ESP32Servo.h>

// Motor pins
const int MOTOR_LEFT_1 = 25;
const int MOTOR_LEFT_2 = 26;
const int MOTOR_RIGHT_1 = 14;
const int MOTOR_RIGHT_2 = 15;

// Servo pins
const int HEAD_PAN_PIN = 18;
const int HEAD_TILT_PIN = 19;
const int ARM_PIN = 5;

Servo headPan;
Servo headTilt;
Servo armServo;

void setup() {
  // Initialize motor pins
  pinMode(MOTOR_LEFT_1, OUTPUT);
  pinMode(MOTOR_LEFT_2, OUTPUT);
  pinMode(MOTOR_RIGHT_1, OUTPUT);
  pinMode(MOTOR_RIGHT_2, OUTPUT);
  
  // Initialize servos
  headPan.attach(HEAD_PAN_PIN);
  headTilt.attach(HEAD_TILT_PIN);
  armServo.attach(ARM_PIN);
  
  // Set initial positions
  headPan.write(90);    // Center
  headTilt.write(90);   // Center
  armServo.write(0);    // Down position
  
  Serial.begin(115200);
  Serial.println("Robot ready for movement!");
}

void moveForward() {
  digitalWrite(MOTOR_LEFT_1, HIGH);
  digitalWrite(MOTOR_LEFT_2, LOW);
  digitalWrite(MOTOR_RIGHT_1, HIGH);
  digitalWrite(MOTOR_RIGHT_2, LOW);
}

void moveBackward() {
  digitalWrite(MOTOR_LEFT_1, LOW);
  digitalWrite(MOTOR_LEFT_2, HIGH);
  digitalWrite(MOTOR_RIGHT_1, LOW);
  digitalWrite(MOTOR_RIGHT_2, HIGH);
}

void turnLeft() {
  digitalWrite(MOTOR_LEFT_1, LOW);
  digitalWrite(MOTOR_LEFT_2, HIGH);
  digitalWrite(MOTOR_RIGHT_1, HIGH);
  digitalWrite(MOTOR_RIGHT_2, LOW);
}

void turnRight() {
  digitalWrite(MOTOR_LEFT_1, HIGH);
  digitalWrite(MOTOR_LEFT_2, LOW);
  digitalWrite(MOTOR_RIGHT_1, LOW);
  digitalWrite(MOTOR_RIGHT_2, HIGH);
}

void stopMotors() {
  digitalWrite(MOTOR_LEFT_1, LOW);
  digitalWrite(MOTOR_LEFT_2, LOW);
  digitalWrite(MOTOR_RIGHT_1, LOW);
  digitalWrite(MOTOR_RIGHT_2, LOW);
}

void loop() {
  // Test movements
  moveForward();
  delay(2000);
  stopMotors();
  delay(1000);
  
  moveBackward();
  delay(2000);
  stopMotors();
  delay(1000);
  
  turnLeft();
  delay(1000);
  stopMotors();
  delay(1000);
  
  turnRight();
  delay(1000);
  stopMotors();
  delay(3000);
}
```

#### Step 5: Head Movement Programming (1 hour)

```cpp
// Add to previous program
void lookLeft() {
  headPan.write(45);
  delay(500);
}

void lookRight() {
  headPan.write(135);
  delay(500);
}

void lookCenter() {
  headPan.write(90);
  delay(500);
}

void nodHead() {
  headTilt.write(60);
  delay(500);
  headTilt.write(120);
  delay(500);
  headTilt.write(90);
  delay(500);
}
```

#### Step 6: Arm Waving Gesture (1 hour)

```cpp
// Add to previous program
void waveArm() {
  for(int i = 0; i < 3; i++) {
    armServo.write(90);   // Up
    delay(500);
    armServo.write(45);   // Middle
    delay(500);
  }
  armServo.write(0);      // Down
  delay(500);
}
```

### Day 3: Smartphone Control

#### Step 7: WiFi Web Server Setup (2 hours)

```cpp
// Day 3 Complete Program with Web Control
#include <WiFi.h>
#include <WebServer.h>
#include <ESP32Servo.h>

// WiFi credentials
const char* ssid = "RobotControl";
const char* password = "robot123";

WebServer server(80);

// Previous motor and servo code here...

void setup() {
  // Previous setup code...
  
  // Start WiFi Access Point
  WiFi.softAP(ssid, password);
  Serial.println("WiFi AP started");
  Serial.print("IP address: ");
  Serial.println(WiFi.softAPIP());
  
  // Setup web server routes
  server.on("/", handleRoot);
  server.on("/move", handleMove);
  server.on("/head", handleHead);
  server.on("/wave", handleWave);
  
  server.begin();
  Serial.println("Web server started");
}

void loop() {
  server.handleClient();
}

void handleRoot() {
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <title>My Robot Control</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial; text-align: center; background: #f0f0f0; }
        .container { max-width: 400px; margin: 0 auto; padding: 20px; }
        button { 
            font-size: 20px; 
            padding: 15px 30px; 
            margin: 10px; 
            border: none; 
            border-radius: 10px; 
            background: #4CAF50; 
            color: white; 
            cursor: pointer; 
        }
        button:active { background: #45a049; }
        .movement { background: #2196F3; }
        .head { background: #FF9800; }
        .gesture { background: #9C27B0; }
        .stop { background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 My Robot</h1>
        
        <h2>Movement</h2>
        <button class="movement" onclick="move('forward')">↑ Forward</button><br>
        <button class="movement" onclick="move('left')">← Left</button>
        <button class="stop" onclick="move('stop')">⏹ Stop</button>
        <button class="movement" onclick="move('right')">→ Right</button><br>
        <button class="movement" onclick="move('backward')">↓ Backward</button>
        
        <h2>Head Control</h2>
        <button class="head" onclick="head('left')">👈 Look Left</button>
        <button class="head" onclick="head('center')">👀 Center</button>
        <button class="head" onclick="head('right')">👉 Look Right</button>
        
        <h2>Gestures</h2>
        <button class="gesture" onclick="wave()">👋 Wave</button>
    </div>

    <script>
        function move(direction) {
            fetch('/move?dir=' + direction);
        }
        
        function head(direction) {
            fetch('/head?dir=' + direction);
        }
        
        function wave() {
            fetch('/wave');
        }
    </script>
</body>
</html>
)";
  server.send(200, "text/html", html);
}

void handleMove() {
  String direction = server.arg("dir");
  
  if(direction == "forward") moveForward();
  else if(direction == "backward") moveBackward();
  else if(direction == "left") turnLeft();
  else if(direction == "right") turnRight();
  else if(direction == "stop") stopMotors();
  
  server.send(200, "text/plain", "OK");
}

void handleHead() {
  String direction = server.arg("dir");
  
  if(direction == "left") lookLeft();
  else if(direction == "right") lookRight();
  else if(direction == "center") lookCenter();
  
  server.send(200, "text/plain", "OK");
}

void handleWave() {
  waveArm();
  server.send(200, "text/plain", "OK");
}
```

## 🎯 Learning Objectives

### Day 1 Objectives
- Understand basic robot components
- Learn safe assembly practices
- Introduction to microcontroller programming
- Basic electronics and wiring

### Day 2 Objectives
- Motor control programming concepts
- Servo motor operation
- Sequential programming logic
- Basic robotics movements

### Day 3 Objectives
- WiFi communication basics
- Web server concepts
- User interface design
- System integration and testing

## 🚀 Extensions & Challenges

### Easy Extensions
1. Add more LED patterns
2. Create different movement patterns
3. Add sound effects with buzzer
4. Program different arm gestures

### Medium Challenges
1. Add obstacle detection with ultrasonic sensor
2. Create autonomous movement patterns
3. Add more complex head movements
4. Implement simple voice commands

### Advanced Projects
1. Add camera for basic vision
2. Implement smartphone app control
3. Add multiple robots communication
4. Create robot competitions

## 🔧 Troubleshooting Guide

### Common Issues

**Robot doesn't move:**
- Check battery charge
- Verify motor connections
- Test motor driver wiring
- Check code upload

**WiFi not connecting:**
- Verify ESP32 power
- Check serial monitor for IP address
- Ensure smartphone on same network
- Reset ESP32 and try again

**Servos not moving:**
- Check servo power connections
- Verify servo pin assignments
- Test with simple servo sweep program
- Check for loose connections

**Web interface not loading:**
- Check ESP32 IP address in serial monitor
- Verify smartphone WiFi connection
- Try refreshing browser
- Check for typos in URL

### Getting Help
- Check serial monitor for error messages
- Verify all connections match wiring diagram
- Ask instructor for assistance
- Work with a partner to debug issues

## 📚 Next Steps

### Continue Learning
- Explore Arduino programming tutorials
- Learn about additional sensors
- Study robotics principles
- Join robotics clubs or competitions

### Upgrade Your Robot
- Add more sensors (ultrasonic, camera)
- Implement voice control
- Create mobile app
- Build more complex behaviors

### Share Your Creation
- Document your robot's capabilities
- Share videos of your robot in action
- Teach others what you've learned
- Participate in robotics showcases

## 🏆 Congratulations!

You've successfully built your first humanoid robot! This is just the beginning of your robotics journey. Keep experimenting, learning, and building amazing things!

---

**Workshop Materials Prepared By:** STEM Robotics Education Team  
**Version:** 1.0  
**Last Updated:** 2024
